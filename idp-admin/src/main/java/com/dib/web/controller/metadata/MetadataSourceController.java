package com.dib.web.controller.metadata;


import cn.hutool.core.util.StrUtil;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.aspose.words.SaveOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.common.database.service.DbQuery;
import com.dib.core.database.core.DbColumn;
import com.dib.core.database.core.DbTable;
import com.dib.core.database.core.JsonPage;
import com.dib.core.database.core.PageResult;
import com.dib.metadata.dto.MetadataSourceDto;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.service.MetadataSourceMapper;
import com.dib.metadata.query.DbDataQuery;
import com.dib.metadata.query.MetadataSourceQuery;
import com.dib.metadata.service.MetadataSourceService;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.metadata.vo.MetadataSourceVo;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据源信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-14
 */
@Tag(name = "数据源信息表")
@RestController
@RequestMapping("/sources")
public class MetadataSourceController extends BaseController {

    @Autowired
    private MetadataSourceService metadataSourceService;

    @Autowired
    private MetadataSourceMapper metadataSourceMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getMetadataSourceById(@PathVariable String id) {
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getMetadataSourceById(id);
        return AjaxResult.success(metadataSourceMapper.toVO(metadataSourceEntity));
    }

    @Operation(summary = "获取列表", description = "")
    @GetMapping("/list")
    public AjaxResult getMetadataSourceList() {
        List<MetadataSourceEntity> list = metadataSourceService.getMetadataSourceList();
        List<MetadataSourceVo> collect = list.stream().map(metadataSourceMapper::toVO).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 分页查询信息
     *
     * @param metadataSourceQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameters({
            @Parameter(name = "metadataSourceQuery", description = "查询实体metadataSourceQuery", required = true)
    })
    @GetMapping("/page")
    public AjaxResult getMetadataSourcePage(MetadataSourceQuery metadataSourceQuery) {
        QueryWrapper<MetadataSourceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(metadataSourceQuery.getSourceName()), "s.source_name", metadataSourceQuery.getSourceName());
        IPage<MetadataSourceEntity> page = metadataSourceService.pageWithAuth(new Page<>(metadataSourceQuery.getPageNum(), metadataSourceQuery.getPageSize()), queryWrapper);
        List<MetadataSourceVo> collect = page.getRecords().stream().map(metadataSourceMapper::toVO).collect(Collectors.toList());
        JsonPage<MetadataSourceVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param metadataSourceDto
     * @return
     */
    @Operation(summary = "添加信息", description = "根据metadataSourceDto对象添加信息")
    @Parameter(name = "metadataSourceDto", description = "详细实体metadataSourceDto", required = true)
    @PostMapping()
    public AjaxResult saveMetadataSource(@RequestBody @Validated({ValidationGroups.Insert.class}) MetadataSourceDto metadataSourceDto) {
        metadataSourceDto.setUser(getUsername());
        metadataSourceService.saveMetadataSource(metadataSourceDto);
        return AjaxResult.success();
    }

    /**
     * 修改
     * @param metadataSourceDto
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true),
            @Parameter(name = "metadataSourceDto", description = "详细实体metadataSourceDto", required = true)
    })
    @PutMapping("/{id}")
    public AjaxResult updateMetadataSource(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) MetadataSourceDto metadataSourceDto) {
        metadataSourceDto.setUser(getUsername());
        metadataSourceService.updateMetadataSource(metadataSourceDto);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteMetadataSourceById(@PathVariable String id) {
        metadataSourceService.deleteMetadataSourceById(id);
        return AjaxResult.success();
    }

    @Operation(summary = "批量删除", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteMetadataSourceBatch(@PathVariable List<String> ids) {
        metadataSourceService.deleteMetadataSourceBatch(ids);
        return AjaxResult.success();
    }

    /**
     * 检测数据库连通性
     * @param metadataSourceDto
     * @return
     */
    @Operation(summary = "数据库连通性", description = "根据数据库配置信息检测数据库连通性")
    @Parameter(name = "dataSource", description = "详细实体dataSource", required = true)
    @PostMapping("/checkConnection")
    public AjaxResult checkConnection(@RequestBody @Validated({ValidationGroups.Insert.class}) MetadataSourceDto metadataSourceDto) {
        DbQuery dbQuery = metadataSourceService.checkConnection(metadataSourceDto);
        Boolean valid = dbQuery.valid();
        return valid ? AjaxResult.success("数据库连接成功") : AjaxResult.error("数据库连接有误，请检查数据库配置是否正确");
    }

    /**
     * 数据库表
     * @param id
     * @return
     */
    @Operation(summary = "数据库表", description = "根据数据源的id来获取指定数据库表")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true)
    })
    @GetMapping("/{id}/tables")
    public AjaxResult getDbTables(@PathVariable String id) {
        List<DbTable> tables = metadataSourceService.getDbTables(id);
        return AjaxResult.success(tables);
    }

    /**
     * 数据库表结构
     * @param id
     * @return
     */
    @Operation(summary = "数据库表结构", description = "根据数据源的id来获取指定数据库表的表结构")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true),
            @Parameter(name = "tableName", description = "数据库表", required = true)
    })
    @GetMapping("/{id}/{tableName}/columns")
    public AjaxResult getDbTableColumns(@PathVariable String id, @PathVariable String tableName) {
        List<DbColumn> columns = metadataSourceService.getDbTableColumns(id, tableName);
        return AjaxResult.success(columns);
    }

    @Operation(summary = "获取SQL结果", description = "根据数据源的id来获取SQL结果")
    @Parameter(name = "dbDataQuery", description = "详细实体dbDataQuery", required = true)
    @PostMapping("/queryList")
    public AjaxResult queryList(@RequestBody @Validated DbDataQuery dbDataQuery) {
        DbQuery dbQuery = metadataSourceService.getDbQuery(dbDataQuery.getDataSourceId());
        List<Map<String, Object>> list = dbQuery.queryList(dbDataQuery.getSql());
        return AjaxResult.success(list);
    }

    @Operation(summary = "分页获取SQL结果", description = "根据数据源的id来分页获取SQL结果")
    @Parameter(name = "dbDataQuery", description = "详细实体dbDataQuery", required = true)
    @PostMapping("/queryByPage")
    public AjaxResult queryByPage(@RequestBody @Validated DbDataQuery dbDataQuery) {
        DbQuery dbQuery = metadataSourceService.getDbQuery(dbDataQuery.getDataSourceId());
        PageResult<Map<String, Object>> page = dbQuery.queryByPage(dbDataQuery.getSql(), dbDataQuery.getOffset(), dbDataQuery.getPageSize());
        page.setPageNum(dbDataQuery.getPageNum()).setPageSize(dbDataQuery.getPageSize());
        return AjaxResult.success(page);
    }

    @Operation(summary = "同步", description = "根据url的id来指定同步对象")
    @Parameter(name = "id", description = "ID", required = true)
    @PostMapping("/sync/{id}")
    public AjaxResult syncMetadata(@PathVariable String id) {
        metadataSourceService.syncMetadata(id);
        return AjaxResult.success();
    }

    @Operation(summary = "数据库设计文档", description = "根据url的id来指定生成数据库设计文档对象")
    @Parameter(name = "id", description = "ID", required = true)
    @PostMapping("/word/{id}")
    public void wordMetadata(@PathVariable String id, HttpServletResponse response) throws Exception {
        // 清空response
        response.reset();
        // 设置response的Header
        response.setContentType("application/octet-stream;charset=utf-8");
        // 设置content-disposition响应头控制浏览器以下载的形式打开文件
        response.addHeader("Content-Disposition", "attachment;filename=" + new String("数据库设计文档.doc".getBytes()));
        Document doc = metadataSourceService.wordMetadata(id);
        OutputStream out = response.getOutputStream();
        doc.save(out, SaveOptions.createSaveOptions(SaveFormat.DOC));
        out.flush();
        out.close();
    }

    /**
     * 刷新参数缓存
     *
     * @return
     */
    @GetMapping("/refresh")
    public AjaxResult refreshMetadata() {
        metadataSourceService.refreshMetadata();
        return AjaxResult.success();
    }


}
