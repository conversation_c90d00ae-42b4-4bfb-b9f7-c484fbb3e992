package com.dib.web.controller.metadata;

import com.dib.common.core.domain.AjaxResult;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.service.MetadataCreateDataBaseAndTableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "创建数据库语句")
@RestController
@RequestMapping("/createDataBase")
public class MetadataCreateDataBaseController {


    @Autowired
    private MetadataCreateDataBaseAndTableService metadataCreateTableService;


    /**
     * 根据选择字段创建建表语句
     *
     * @param metadataCreateTableDto
     * @return
     */
    @Operation(summary = "创建数据库", description = "创建数据库")
    @PostMapping("/queryCreateDataBaseSql")
    public AjaxResult queryCreateTableSql(@RequestBody MetadataCreateSqlDto metadataCreateTableDto) {
        return AjaxResult.success("创建数据库成功",metadataCreateTableService.createDataBase(metadataCreateTableDto));
    }

}
