package com.dib.web.controller.metadata;

import com.dib.common.core.domain.AjaxResult;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.service.MetadataCreateDataBaseAndTableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "创建建表语句")
@RestController
@RequestMapping("/createTable")
public class MetadataCreateTableController {

    @Autowired
    private MetadataCreateDataBaseAndTableService metadataCreateTableService;


    /**
     * 根据选择字段创建建表语句
     *
     * @param metadataCreateTableDto
     * @return
     */
    @Operation(summary = "查询创建sql", description = "根据选择字段创建建表语句")
    @PostMapping("/queryCreateTableSql")
    public AjaxResult queryCreateTableSql(@RequestBody MetadataCreateSqlDto metadataCreateTableDto) {
        return AjaxResult.success("创建sql成功",metadataCreateTableService.createSql(metadataCreateTableDto));
    }


    /**
     * 根据选择字段创建建表语句
     *
     * @param metadataCreateTableDto
     * @return
     */
    @Operation(summary = "查询创建sql", description = "根据选择字段创建建表语句")
    @PostMapping("/runCreateTableSql")
    public AjaxResult createTable(@RequestBody MetadataCreateSqlDto metadataCreateTableDto){


        return AjaxResult.success("创建表成功",metadataCreateTableService.runSql(metadataCreateTableDto));
    }
}
