package com.dib.web.controller.dib;

import com.dib.bigdata.core.util.LocalCacheUtil;
import com.dib.bigdata.entity.JobDatasource;
import com.dib.bigdata.service.JobDatasourceService;
import com.dib.common.core.data.R;
import com.dib.common.core.domain.AjaxResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;

/**
 * jdbc数据源配置控制器层
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2019-07-30
 */
@RestController
@RequestMapping("/api/jobJdbcDatasource")
@Tag(name = "jdbc数据源配置控制器层")
public class JobDatasourceController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private JobDatasourceService jobJdbcDatasourceService;

    /**
     * 分页查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping
    @Operation(summary = "分页查询所有数据")
    @Parameter(name = "current", description = "当前页", example = "1", required = true)
    @Parameter(name = "size", description = "一页大小", example = "10", required = true)
    @Parameter(name = "ifCount", description = "是否查询总数", example = "true")
    @Parameter(name = "ascs", description = "升序字段，多个用逗号分隔")
    @Parameter(name = "descs", description = "降序字段，多个用逗号分隔")
    @PreAuthorize("@ss.hasPermi('datax:Datasource:list')")
    public R selectAll() {
        return R.ok(jobJdbcDatasourceService.selectAll());
    }

    /**
     * 获取所有数据源
     * @return
     */
    @Operation(summary = "获取所有数据源")
    @GetMapping("/all")
    @PreAuthorize("@ss.hasPermi('datax:Datasource:query')")
    public R selectAllDatasource() {
        return R.ok(this.jobJdbcDatasourceService.selectAllDatasource());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Operation(summary = "通过主键查询单条数据")
    @GetMapping("{id}")
    @PreAuthorize("@ss.hasPermi('datax:Datasource:query')")
    public R selectOne(@PathVariable Serializable id) {
        return R.ok(this.jobJdbcDatasourceService.getById(id));
    }

    /**
     * 新增数据
     *
     * @param entity 实体对象
     * @return 新增结果
     */
    @Operation(summary = "新增数据")
    @PostMapping
    @PreAuthorize("@ss.hasPermi('datax:Datasource:add')")
    public R insert(@RequestBody JobDatasource entity) {
        return R.ok(this.jobJdbcDatasourceService.saveJobDatasource(entity));
    }

    /**
     * 修改数据
     *
     * @param entity 实体对象
     * @return 修改结果
     */
    @PutMapping
    @Operation(summary = "修改数据")
    @PreAuthorize("@ss.hasPermi('datax:Datasource:edit')")
    public R update(@RequestBody JobDatasource entity) {
        LocalCacheUtil.remove(entity.getDatasourceName());
        JobDatasource d = jobJdbcDatasourceService.getById(entity.getId());
        if (null != d.getJdbcUsername() && entity.getJdbcUsername().equals(d.getJdbcUsername())) {
            entity.setJdbcUsername(null);
        }
        if (null != entity.getJdbcPassword() && entity.getJdbcPassword().equals(d.getJdbcPassword())) {
            entity.setJdbcPassword(null);
        }
        return R.ok(this.jobJdbcDatasourceService.updateJobDatasource(entity));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @Operation(summary = "删除数据")
    @PreAuthorize("@ss.hasPermi('datax:Datasource:remove')")
    public R delete(@RequestParam("idList") List<Long> idList) {
        return R.ok(this.jobJdbcDatasourceService.removeJobDatasourceIds(idList));
    }

    /**
     * 测试数据源
     * @param jobJdbcDatasource
     * @return
     */
    @PostMapping("/test")
    @Operation(summary = "测试数据")
    public R dataSourceTest (@RequestBody JobDatasource jobJdbcDatasource) throws IOException {
        return R.ok(jobJdbcDatasourceService.dataSourceTest(jobJdbcDatasource));
    }



    @Operation(summary = "获取指定菜单列表", description = "")
    @GetMapping("/getSourceListBySysId")
    public AjaxResult getSourceListBySysId(@RequestParam String sysId) {
        return AjaxResult.success(jobJdbcDatasourceService.getSourceListBySysId(sysId));
    }
}