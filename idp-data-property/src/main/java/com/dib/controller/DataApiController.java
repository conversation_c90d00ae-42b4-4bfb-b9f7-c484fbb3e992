package com.dib.controller;

import cn.hutool.core.util.StrUtil;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.aspose.words.SaveOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.market.dto.DataApiDto;
import com.dib.market.dto.SqlParseDto;
import com.dib.market.entity.DataApiEntity;
import com.dib.market.mapstruct.DataApiMapper;
import com.dib.market.query.DataApiQuery;
import com.dib.market.service.DataApiService;
import com.dib.market.vo.DataApiVo;
import com.dib.market.vo.SqlParseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.sf.jsqlparser.JSQLParserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据API信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Tag(name = "数据API信息表")
@RestController
@RequestMapping("market/dataApis")
public class DataApiController {

    @Autowired
    private DataApiService dataApiService;

    @Autowired
    private DataApiMapper dataApiMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getDataApiById(@PathVariable String id) {
        DataApiEntity dataApiEntity = dataApiService.getDataApiById(id);
        return AjaxResult.success(dataApiMapper.toVO(dataApiEntity));
    }

    @Operation(summary = "获取列表", description = "")
    @GetMapping("/list")
    public AjaxResult getDataApiList() {
        QueryWrapper<DataApiEntity> queryWrapper = new QueryWrapper<>();
        List<DataApiEntity> list = dataApiService.list(queryWrapper);
        List<DataApiVo> collect = list.stream().map(dataApiMapper::toVO).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 分页查询信息
     *
     * @param dataApiQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "dataApiQuery", description = "查询实体dataApiQuery", required = true)
    @PostMapping("/page")
    public AjaxResult getDataApiPage(@RequestBody DataApiQuery dataApiQuery) {
        QueryWrapper<DataApiEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dataApiQuery.getApiName()), "api_name", dataApiQuery.getApiName());
        IPage<DataApiEntity> page = dataApiService.page(new Page<>(dataApiQuery.getPageNum(), dataApiQuery.getPageSize()), queryWrapper);
        List<DataApiVo> collect = page.getRecords().stream().map(dataApiMapper::toVO).collect(Collectors.toList());
        JsonPage<DataApiVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param dataApi
     * @return
     */
    @Operation(summary = "添加信息", description = "根据dataApi对象添加信息")
    @Parameter(name = "dataApi", description = "详细实体dataApi", required = true)
    @PostMapping("/saveDataApi")
    public AjaxResult saveDataApi(@RequestBody DataApiDto dataApi) {
        dataApiService.saveDataApi(dataApi);
        return AjaxResult.success();
    }

    /**
     * 修改
     * @param dataApi
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @Parameter(name = "dataApi", description = "详细实体dataApi", required = true)
    @PostMapping("/{id}")
    public AjaxResult updateDataApi(@RequestBody DataApiDto dataApi) {
        dataApiService.updateDataApi(dataApi);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteDataApiById(@PathVariable String id) {
        dataApiService.deleteDataApiById(id);
        return AjaxResult.success();
    }

    @Operation(summary = "批量删除", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteDataApiBatch(@PathVariable List<String> ids) {
        dataApiService.deleteDataApiBatch(ids);
        return AjaxResult.success();
    }

    /**
     * SQL解析
     * @param sqlParseDto
     * @return
     */
    @Operation(summary = "SQL解析")
    @Parameter(name = "sqlParseDto", description = "SQL解析实体sqlParseDto", required = true)
    @PostMapping("/sql/parse")
    public AjaxResult sqlParse(@RequestBody SqlParseDto sqlParseDto) throws SQLException, JSQLParserException {
        SqlParseVo sqlParseVo = dataApiService.sqlParse(sqlParseDto);
        return AjaxResult.success(sqlParseVo);
    }

    /**
     * 拷贝接口
     * @param id
     * @return
     */
    @PostMapping("/{id}/copy")
    public AjaxResult copyDataApi(@PathVariable String id) {
        dataApiService.copyDataApi(id);
        return AjaxResult.success();
    }

    /**
     * 发布接口
     * @param id
     * @return
     */
    @PostMapping(value = "/{id}/release")
    public AjaxResult releaseDataApi(@PathVariable String id){
        dataApiService.releaseDataApi(id);
        return AjaxResult.success();
    }

    /**
     * 注销接口
     * @param id
     * @return
     */
    @PostMapping(value = "/{id}/cancel")
    public AjaxResult cancelDataApi(@PathVariable String id){
        dataApiService.cancelDataApi(id);
        return AjaxResult.success();
    }

    @Operation(summary = "接口文档", description = "根据url的id来指定生成接口文档对象")
    @Parameter(name = "id", description = "ID", required = true)
    @PostMapping("/word/{id}")
    public void wordDataApi(@PathVariable String id, HttpServletResponse response) throws Exception {
        // 清空response
        response.reset();
        // 设置response的Header
        response.setContentType("application/octet-stream;charset=utf-8");
        // 设置content-disposition响应头控制浏览器以下载的形式打开文件
        response.addHeader("Content-Disposition", "attachment;filename=" + new String("接口文档.docx".getBytes()));
        Document doc = dataApiService.wordDataApi(id);
        OutputStream out = response.getOutputStream();
        doc.save(out, SaveOptions.createSaveOptions(SaveFormat.DOCX));
        out.flush();
        out.close();
    }

    @GetMapping("/detail/{id}")
    public AjaxResult getDataApiDetailById(@PathVariable String id) {
        Map<String, Object> map = dataApiService.getDataApiDetailById(id);
        return AjaxResult.success(map);
    }

    /**
     * 查询未绑定资产api
     */
    @GetMapping("/getUnboundAssetDataApi")
    public AjaxResult getUnboundAssetDataApi(@RequestParam String id) {
        return AjaxResult.success(dataApiService.getUnboundAssetDataApi(id));
    }
}
