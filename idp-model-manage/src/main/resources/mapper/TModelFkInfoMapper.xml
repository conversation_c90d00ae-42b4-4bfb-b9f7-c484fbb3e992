<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelFkInfoMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelFkInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="f_name" property="fName" jdbcType="VARCHAR" />
    <result column="f_col" property="fCol" jdbcType="VARCHAR" />
    <result column="relation_table" property="relationTable" jdbcType="VARCHAR" />
    <result column="relation_col" property="relationCol" jdbcType="VARCHAR" />
    <result column="is_enable" property="isEnable" jdbcType="INTEGER" />
    <result column="model_no" property="modelNo" jdbcType="BIGINT" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modiftor_id" property="modiftorId" jdbcType="INTEGER" />
    <result column="modiftor" property="modiftor" jdbcType="VARCHAR" />
    <result column="modify_date" property="modifyDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, f_name, f_col, relation_table, relation_col, is_enable, model_no, creator_id, 
    creator_name, create_date, modiftor_id, modiftor, modify_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_fk_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_fk_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.dib.model.domain.TModelFkInfo" >
    insert into t_model_fk_info (id, f_name, f_col, 
      relation_table, relation_col,
      model_no, creator_id, creator_name, 
      create_date, modiftor_id, modiftor, 
      modify_date)
    values (#{id,jdbcType=BIGINT}, #{fName,jdbcType=VARCHAR}, #{fCol,jdbcType=VARCHAR}, 
      #{relationTable,jdbcType=VARCHAR}, #{relationCol,jdbcType=VARCHAR},
      #{modelNo,jdbcType=BIGINT}, #{creatorId,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{modiftorId,jdbcType=INTEGER}, #{modiftor,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelFkInfo" >
    insert into t_model_fk_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="fName != null" >
        f_name,
      </if>
      <if test="fCol != null" >
        f_col,
      </if>
      <if test="relationTable != null" >
        relation_table,
      </if>
      <if test="relationCol != null" >
        relation_col,
      </if>
      <if test="isEnable != null" >
        is_enable,
      </if>
      <if test="modelNo != null" >
        model_no,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modiftorId != null" >
        modiftor_id,
      </if>
      <if test="modiftor != null" >
        modiftor,
      </if>
      <if test="modifyDate != null" >
        modify_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fName != null" >
        #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fCol != null" >
        #{fCol,jdbcType=VARCHAR},
      </if>
      <if test="relationTable != null" >
        #{relationTable,jdbcType=VARCHAR},
      </if>
      <if test="relationCol != null" >
        #{relationCol,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        #{modelNo,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftorId != null" >
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modiftor != null" >
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null" >
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelFkInfo" >
    update t_model_fk_info
    <set >
      <if test="fName != null" >
        f_name = #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fCol != null" >
        f_col = #{fCol,jdbcType=VARCHAR},
      </if>
      <if test="relationTable != null" >
        relation_table = #{relationTable,jdbcType=VARCHAR},
      </if>
      <if test="relationCol != null" >
        relation_col = #{relationCol,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        is_enable = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        model_no = #{modelNo,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftorId != null" >
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modiftor != null" >
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null" >
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelFkInfo" >
    update t_model_fk_info
    set f_name = #{fName,jdbcType=VARCHAR},
      f_col = #{fCol,jdbcType=VARCHAR},
      relation_table = #{relationTable,jdbcType=VARCHAR},
      relation_col = #{relationCol,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=INTEGER},
      model_no = #{modelNo,jdbcType=BIGINT},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modiftor_id = #{modiftorId,jdbcType=INTEGER},
      modiftor = #{modiftor,jdbcType=VARCHAR},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>