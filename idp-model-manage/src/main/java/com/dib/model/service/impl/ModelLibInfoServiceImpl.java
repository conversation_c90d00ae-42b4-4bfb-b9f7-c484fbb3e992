package com.dib.model.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.utils.StringUtils;
import com.dib.common.utils.bean.BeanUtils;
import com.dib.model.domain.TModelBase;
import com.dib.model.domain.TModelLibInfo;
import com.dib.model.mapper.TModelLibInfoMapper;
import com.dib.model.mapstruct.ModelLibInfoConvertor;
import com.dib.model.service.TModelBaseService;
import com.dib.model.service.TModelLibInfoService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelLibInfoVo;
import com.dib.model.vo.TModelLibUpdateInfoVo;
import com.dib.model.vo.TreeNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ModelLibInfoServiceImpl extends ServiceImpl<TModelLibInfoMapper, TModelLibInfo> implements TModelLibInfoService {

    private static final int BATCH_SIZE = 500;

    @Autowired
    private TModelBaseService modelBaseService;

    @Override
    public List<TreeNode> buildNavigationTree() {
        // 1. 获取所有数据
        List<TModelLibInfo> modelLibInfos = this.lambdaQuery().list();
        List<TModelBase> modelBaseInfos = modelBaseService.list();

        // 2. 构建节点映射
        Map<Long, TreeNode> nodeMap = new HashMap<>();

        // 3. 处理模型库信息
        modelLibInfos.forEach(info -> {
            TreeNode node = new TreeNode();
            node.setId(info.getId().toString());
            node.setUuid(UUID.randomUUID().toString());
            node.setParentId(info.getPid());
            node.setTitleName(info.getModelLibName());
            node.setNodeType(new BigDecimal(info.getModelType()).shortValue());
            nodeMap.put(info.getId(), node);
        });

        // 4. 处理模型基础信息
        modelBaseInfos.forEach(info -> {
            TreeNode node = new TreeNode();
            node.setId(info.getId().toString());
            node.setUuid(UUID.randomUUID().toString());
            node.setParentId(info.getModelLibNo());
            node.setTitleName(info.getTitle());
            node.setNodeType(info.getModelType() == null ? 4 : info.getModelType().shortValue());
            nodeMap.put(info.getId(), node);
        });

        // 5. 构建树结构
        List<TreeNode> rootNodes = new ArrayList<>();
        nodeMap.values().forEach(node -> {
            Long parentId = node.getParentId();
            if (parentId == null || parentId == 0L) {
                rootNodes.add(node);
            } else {
                TreeNode parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.addChild(node);
                }
            }
        });

        return rootNodes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelLibInfo create(TModelLibInfo modelLibInfo) {
        modelLibInfo.setId(SnowflakeUtils.nextId());
        save(modelLibInfo);
        return modelLibInfo;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelLibInfoVo createFromVo(TModelLibInfoVo modelLibInfoVo) {
        TModelLibInfo modelLibInfo = convertToEntity(modelLibInfoVo);
        modelLibInfo.setId(SnowflakeUtils.nextId());
        TModelLibInfo createdModelLibInfo = create(modelLibInfo);
        return convertToVo(createdModelLibInfo);
    }

    @Override
    public List<TModelLibInfo> getAll() {
        return list();
    }

    @Override
    public TModelLibInfo getModelLibInfo(Long id) {
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelLibInfo update(TModelLibInfoVo modelLibInfo) {
        TModelLibInfo entity = convertToEntity(modelLibInfo);
        updateById(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        return removeById(id);
    }

    @Override
    public List<TModelLibInfoVo> getModelLibInfoList() {
        List<TModelLibInfo> list = list();
        List<TModelLibInfoVo> tModelLibInfoVos = convertToVo(list);
        List<TModelBase> modelBaseList = modelBaseService.lambdaQuery().select(TModelBase::getModelLibNo).list();
        if(CollectionUtils.isNotEmpty(modelBaseList)){
            Map<Long, List<TModelBase>> modelMap = modelBaseList.stream().collect(Collectors.groupingBy(TModelBase::getModelLibNo));
            tModelLibInfoVos.forEach(vo -> {
                vo.setModelCount(modelMap.getOrDefault(vo.getId(), Collections.emptyList()).size());
            });
        }
        return tModelLibInfoVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelLibInfo updateById(TModelLibUpdateInfoVo modelLibInfoVo) {
        // 获取原有实体
        TModelLibInfo existingEntity = getById(modelLibInfoVo.getId());
        if (existingEntity != null) {
            // 将更新VO转换为实体
            TModelLibInfo updatedEntity = convertToEntity(modelLibInfoVo);
            // 更新实体
            updateById(updatedEntity);
            return updatedEntity;
        }
        return null;
    }
    
    @Override
    public TModelLibInfoVo convertToVo(TModelLibInfo entity) {
        if (entity == null) {
            return null;
        }
        return ModelLibInfoConvertor.INSTANT.toVO(entity);
    }
    
    @Override
    public List<TModelLibInfoVo> convertToVo(List<TModelLibInfo> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return ModelLibInfoConvertor.INSTANT.toVO(entityList);
    }
    
    @Override
    public TModelLibInfo convertToEntity(TModelLibInfoVo vo) {
        if (vo == null) {
            return null;
        }
        return ModelLibInfoConvertor.INSTANT.toEntity(vo);
    }
    
    @Override
    public List<TModelLibInfo> convertToEntity(List<TModelLibInfoVo> voList) {
        if (voList == null || voList.isEmpty()) {
            return new ArrayList<>();
        }
        return ModelLibInfoConvertor.INSTANT.toEntity(voList);
    }
    
    @Override
    public TModelLibInfo convertToEntity(TModelLibUpdateInfoVo vo) {
        if (vo == null) {
            return null;
        }
        TModelLibInfo entity = new TModelLibInfo();
        entity.setId(vo.getId());
        entity.setModelLibName(vo.getModelLibName());
        entity.setDatabasePoolId(vo.getDatabasePoolId());
        entity.setMark(vo.getMark());
        entity.setPid(vo.getPid());
        entity.setModelType(vo.getModelType());
        entity.setSourceId(vo.getSourceId());
        return entity;
    }
}
