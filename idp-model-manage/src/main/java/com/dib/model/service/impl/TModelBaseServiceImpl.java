package com.dib.model.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.database.constants.DbType;
import com.dib.common.utils.DateUtils;
import com.dib.common.utils.StringUtils;
import com.dib.common.utils.uuid.UUID;
import com.dib.metadata.dto.MetadataColumnMarketDto;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.dto.SqlConsoleDto;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.enums.ActionTypeEnum;
import com.dib.metadata.enums.SqlTypeEnum;
import com.dib.metadata.service.*;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.SqlConsoleVo;
import com.dib.model.domain.*;
import com.dib.model.enums.CreateTypeEnum;
import com.dib.model.ModelTableVo;
import com.dib.model.enums.ModelTypeEnum;
import com.dib.model.mapper.TModelBaseMapper;
import com.dib.model.mapper.TModelColumnMapper;
import com.dib.model.mapstruct.ModelColumnConvertor;
import com.dib.model.mapstruct.ModelConvertor;
import com.dib.model.service.*;
import com.dib.model.utils.*;
import com.dib.model.mapstruct.ModelFkInfoConvertor;
import com.dib.model.service.TModelBaseService;
import com.dib.model.service.TModelColumnService;
import com.dib.model.service.TModelForeignKeyService;
import com.dib.model.service.TModelPrimaryKeyService;
import com.dib.model.utils.ModelException;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.utils.SqlSecurityValidator;
import com.dib.model.utils.SysResultCode;
import com.dib.model.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TModelBaseServiceImpl extends ServiceImpl<TModelBaseMapper, TModelBase> implements TModelBaseService {

    @Autowired
    private TModelColumnService tModelColumnService;

    @Autowired
    private MetadataColumnService metadataColumnService;

    @Autowired
    private MetadataSourceService metadataSourceService;

    @Autowired
    private SqlConsoleService sqlConsoleService;

    @Autowired
    private MetadataCreateDataBaseAndTableService metadataCreateTableService;

    @Autowired
    private TModelColumnMapper tModelColumnMapper;

    @Autowired
    private TModelPrimaryKeyService tModelPrimaryKeyService;

    @Autowired
    private TModelForeignKeyService tModelForeignKeyService;

    @Autowired
    private TModelIndexService tModelIndexService;

    @Autowired
    private TableCreationUtils tableCreationUtils;


    @Override
    @Transactional
    public TModelBaseInfoVo create(TModelBaseAddVo modelBase) throws SQLException {
        //保存模型
        TModelBase tModelBase = ModelConvertor.INSTANT.toEntity(modelBase);
        tModelBase.setId(SnowflakeUtils.nextId());
        // 设置UUID和模型类型
        tModelBase.setModelUuid(UUID.randomUUID().toString());
        tModelBase.setModelType(ModelTypeEnum.PHYSICAL_MODEL.getType());
        //固定数据源为doris
        tModelBase.setSourceId(tableCreationUtils.getDorisSourceId());
        tModelBase.setIsEnable(1);
        save(tModelBase);
        //将模型字段转换成表结构字段
        List<MetadataColumnMarketDto> metadataColumnEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(modelBase.getTModelColumns())) {
            //保存模型字段
            List<TModelColumn> tModelColumns = ModelColumnConvertor.INSTANT.toEntityList(modelBase.getTModelColumns());
            for (TModelColumnVo tModelColumn : modelBase.getTModelColumns()) {
                tModelColumn.setModelNo(tModelBase.getId());
                tModelColumn.setId(SnowflakeUtils.nextId());
                MetadataColumnMarketDto metadataColumnEntity = new MetadataColumnMarketDto();
                if (tModelColumn.getTitle().equals("id")) {
                    metadataColumnEntity.setColumnKey("1");
                }
                metadataColumnEntity.setSourceId(tModelBase.getSourceId());
                metadataColumnEntity.setTableName(tModelBase.getTName());
                metadataColumnEntity.setColumnName(tModelColumn.getTName());
                metadataColumnEntity.setColumnComment(tModelColumn.getRemark());
                metadataColumnEntity.setDataLength(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.getTLength(), 0)).toString());
                metadataColumnEntity.setDataPrecision(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.getDecimalDigits(), 0)).toString());
                metadataColumnEntity.setColumnNullable(tModelColumn.getIsNull() == 1 ? "Y" : "N");
                metadataColumnEntity.setActionType(ActionTypeEnum.ADD_COLUMN.getCode());
                metadataColumnEntities.add(metadataColumnEntity);
            }
            tModelColumnService.saveOrUpdateBatch(tModelColumns);
        }

        if (!CreateTypeEnum.CREATE_TYPE_SQL.getCode().equals(modelBase.getCreateType())){
            String tableId = tableCreationUtils.createTableAndGetId(tModelBase, metadataColumnEntities);
            tModelBase.setTableId(tableId);
        }
        updateById(tModelBase);
        return ModelConvertor.INSTANT.toInfoVO(tModelBase);
    }

    @Override
    public List<TModelBaseVo> getModelBaseList(Long modelLibNo) {

        // 使用 QueryWrapper 构建查询条件
        // 调用 Mapper 的 selectList 方法查询数据
        List<TModelBase> modelBaseList = this.lambdaQuery().eq(TModelBase::getModelLibNo, modelLibNo).list();

        // 使用MapStruct进行对象转换
        return ModelConvertor.INSTANT.toVO(modelBaseList);
    }


    @Override
    @Transactional
    public boolean importModelList(List<TModelBaseVo> modelBasesList) {
        // 验证模型库编号不能为空
        if (modelBasesList.stream().anyMatch(vo -> vo.getModelLibNo() == null)) {
            throw new ModelException("模型库编号不能为空");
        }
        
        // 使用MapStruct进行对象转换
        List<TModelBase> modelBases = ModelConvertor.INSTANT.fromVO(modelBasesList);
        return saveOrUpdateBatch(modelBases, modelBases.size());
    }

    @Override
    public List<MetadataColumnVo> queryColList(SelectColumnVo selectColumnVo) {
        List<String> tableIds = selectColumnVo.getTableId();
        List<String> columnNames = selectColumnVo.getColumnlist();
        List<MetadataColumnVo> metadataColumnEntityList = metadataColumnService.getColumnsByTableIds(tableIds);
        metadataColumnEntityList.removeIf(column -> !columnNames.contains(column.getColumnName()));
        return metadataColumnEntityList;
    }

    @Override
    public List<TModelBaseInfoVo> queryAllModelList() {
        List<TModelBase> modelBaseList = this.lambdaQuery().eq(TModelBase::getModelType, ModelTypeEnum.PHYSICAL_MODEL.getType()).list();
        return ModelConvertor.INSTANT.toInfoVO(modelBaseList);
    }

    @Override
    public IndictorDimVo queryIndicAndDimList(String modelNo) {
        IndictorDimVo indictorDimVo = new IndictorDimVo();
        if (StringUtils.isNotBlank(modelNo)) {
            Long modelID = Long.parseLong(modelNo);
            TModelBaseInfoVo modelBaseInfo = getModelBaseInfo(modelID);
            if (modelBaseInfo != null) {
                List<IndictorAssetVo> indictorAssetVoList = new ArrayList<>();
                List<DimAssetVo> dimAssetVoList = new ArrayList<>();
                List<TModelColumn> modelColumnList = tModelColumnMapper.selectByModelNo(modelID);

                for (TModelColumn modelColumn : modelColumnList) {

                    if (modelColumn.getIsDim() == 1) {
                        //维度
                        DimAssetVo dimAssetVo = new DimAssetVo();
                        dimAssetVo.setName(modelColumn.getTitle());
                        dimAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        dimAssetVo.setColumnIndex(modelColumn.getFieldSortNum());
                        dimAssetVo.setType(modelColumn.getDataLevel());
                        dimAssetVo.setDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                        dimAssetVo.setDateStyle(modelColumn.getDataLevel());
                        dimAssetVo.setFieldShortName(modelColumn.getAliasName());
                        dimAssetVo.setDeType(0);
                        dimAssetVo.setExtField(0);
                        dimAssetVo.setGroupType("d");
                        dimAssetVoList.add(dimAssetVo);
                    } else {
                        //指标
                        IndictorAssetVo indictorAssetVo = new IndictorAssetVo();
                        indictorAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        indictorAssetVo.setName(modelColumn.getTName());
                        indictorAssetVo.setColumnIndex(modelColumn.getFieldSortNum());
                        indictorAssetVo.setCustomSort(modelColumn.getFieldSortNum());
                        indictorAssetVo.setChecked(true);
                        indictorAssetVo.setDataeaseName(modelColumn.getTName());
                        indictorAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        indictorAssetVo.setType(modelColumn.getDataLevel());
                        indictorAssetVo.setDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                        indictorAssetVo.setDateStyle(modelColumn.getDataLevel());
                        indictorAssetVo.setFieldShortName(modelColumn.getAliasName());
                        indictorAssetVo.setGroupType("q");
                        indictorAssetVo.setDeType(2);
                        indictorAssetVo.setExtField(1);
                        indictorAssetVo.setChartType("bar");
                        indictorAssetVoList.add(indictorAssetVo);
                    }
                }
                indictorDimVo.setDimAssetVoList(dimAssetVoList);
                indictorDimVo.setIndictorAssetVoList(indictorAssetVoList);
            }
        }
        return indictorDimVo;
    }

    // 在queryModelData方法中添加安全校验和异常处理
    @Override
    public List<SqlConsoleVo> queryModelData(List<String> indictorAttrs, String modelNo) throws SQLException {
        // 参数校验
        if (StringUtils.isEmpty(modelNo)) {
            throw new ModelException(SysResultCode.PARAM_ERROR.getCode(), "模型编号不能为空");
        }
        
        // 校验modelNo是否为有效数字
        try {
            new BigDecimal(modelNo);
        } catch (NumberFormatException e) {
            throw new ModelException(SysResultCode.PARAM_ERROR.getCode(), "模型编号格式不正确");
        }
        
        // 校验列名安全性
        if (indictorAttrs != null && !indictorAttrs.isEmpty()) {
            for (String attr : indictorAttrs) {
                if (!SqlSecurityValidator.isValidColumnName(attr)) {
                    throw new ModelException(SysResultCode.PARAM_ERROR.getCode(), "列名包含非法字符: " + attr);
                }
            }
        }
        
        try {
            SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
            sqlConsoleDto.setSqlKey(new BigDecimal(DateTime.now().getTime()).toString());
            
            TModelBaseInfoVo tModelBaseInfo = getModelBaseInfo(new BigDecimal(modelNo).longValue());
            if (tModelBaseInfo == null) {
                throw new ModelException(SysResultCode.NULL_DATA.getCode(), "未找到对应的模型信息");
            }

            MetadataSourceEntity metadataSourceEntity = tableCreationUtils.getDorisSource();
            sqlConsoleDto.setSourceId(metadataSourceEntity.getId());
            sqlConsoleDto.setTableName(tModelBaseInfo.getTName());

            if (metadataSourceEntity == null || metadataSourceEntity.getDbSchema() == null) {
                throw new ModelException(SysResultCode.NULL_DATA.getCode(), "数据源信息不存在或配置不完整");
            }
            
            String DbName = metadataSourceEntity.getDbSchema().getDbName();
            String sid = metadataSourceEntity.getDbSchema().getSid();
            
            // 校验数据库名和schema名的安全性
            if (!SqlSecurityValidator.isValidTableName(DbName) || !SqlSecurityValidator.isValidTableName(sid)) {
                throw new ModelException(SysResultCode.PARAM_ERROR.getCode(), "数据库名或schema名包含非法字符");
            }
            
            if (!CreateTypeEnum.CREATE_TYPE_SQL.getCode().equals(tModelBaseInfo.getCreateType())) {
                String sqlText;
                if (indictorAttrs != null && !indictorAttrs.isEmpty()) {
                    String queryColumn = String.join(",", indictorAttrs);
                    sqlText = "select " + queryColumn + " from " + DbName + "." + sid + "." + tModelBaseInfo.getTName() + ";";
                } else {
                    sqlText = "select * from " + DbName + "." + sid + "." + tModelBaseInfo.getTName() + ";";
                }
                
                // 校验生成的SQL安全性
                if (!SqlSecurityValidator.isValidSelectSql(sqlText)) {
                    throw new ModelException(SysResultCode.PARAM_ERROR.getCode(), "生成的SQL语句存在安全风险");
                }
                
                sqlConsoleDto.setSqlText(sqlText);
            } else {
                sqlConsoleDto.setSqlText(tModelBaseInfo.getModelSql());
            }
            List<SqlConsoleVo> sqlConsoleVos = sqlConsoleService.sqlRun(sqlConsoleDto);
            return sqlConsoleVos;
        }
        
        catch (ModelException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询模型数据失败, modelNo: {}, error: {}", modelNo, e.getMessage(), e);
            throw new ModelException(SysResultCode.SYSTEM_ERROR.getCode(), "查询模型数据失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean tableModelStructSync(String modelNo) {
        TModelBaseInfoVo modelBaseInfo = getModelBaseInfo(new BigDecimal(modelNo).longValue());
        TModelBase modelBase = ModelConvertor.INSTANT.fromInfoVO(modelBaseInfo);
        //基于sql创建时，仅更新sql
        if(CreateTypeEnum.CREATE_TYPE_SQL.getCode().equals(modelBase.getCreateType())){
            modelBase.setModelSql(modelBase.getModelSql());
            updateById(modelBase);
            return true;
        }
        //查询最新表结构
        List<TModelColumn> tModelColumns = tModelColumnMapper.selectByModelNo(new BigDecimal(modelNo).longValue());
        //查询表结构
        List<MetadataColumnVo> metadataColumnVos = metadataColumnService.getColumnsByTableIds(Arrays.asList(modelBase.getTableId()));

        MetadataCreateSqlDto metadataCreateSqlDto = new MetadataCreateSqlDto();
        metadataCreateSqlDto.setTargetTableName(modelBase.getTName());
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getBaseMapper().selectById(modelBase.getSourceId());
        //重新创建表
        List<MetadataColumnMarketDto> columnMarketDtos = new ArrayList<>();
        for (TModelColumn tModelColumn : tModelColumns) {
            MetadataColumnMarketDto metadataColumnMarketDto = new MetadataColumnMarketDto();
            metadataColumnMarketDto.setTableId(modelBase.getTableId());
            metadataColumnMarketDto.setColumnName(tModelColumn.getTName());
            metadataColumnMarketDto.setColumnComment(tModelColumn.getRemark());
            metadataColumnMarketDto.setDataType(tModelColumn.getDataLevel());
            metadataColumnMarketDto.setDataLength(new BigDecimal(tModelColumn.getTLength() == null ? 0 : tModelColumn.getTLength()).toString());
            metadataColumnMarketDto.setSourceId(modelBase.getSourceId());
            metadataColumnMarketDto.setTableName(modelBase.getTName());
            String columnName = tModelColumn.getTName();
            Boolean isExist = metadataColumnVos != null && columnName != null && metadataColumnVos.stream().filter(Objects::nonNull).map(MetadataColumnVo::getColumnName).anyMatch(columnName::equals);
            if (!isExist) {
                metadataColumnMarketDto.setActionType(ActionTypeEnum.ADD_COLUMN.getCode());
            }
//            else {
//                metadataColumnMarketDto.setActionType("addColumn");
//            }
            //列的操作
            columnMarketDtos.add(metadataColumnMarketDto);
        }
        metadataCreateSqlDto.setMetadataColumnMarketDtoList(columnMarketDtos);
        metadataCreateSqlDto.setTargetSchemaName(metadataSourceEntity.getDbSchema().getSid());
        metadataCreateSqlDto.setSqlType(SqlTypeEnum.UPDATE_TABLE.getCode());
        metadataCreateSqlDto.setTargetId(metadataSourceEntity.getId());
        String sqlCreatTable = metadataCreateTableService.createSql(metadataCreateSqlDto);
        metadataCreateSqlDto.setTableSql(sqlCreatTable);
        log.debug("updateTable:" + sqlCreatTable);
        metadataCreateTableService.runSql(metadataCreateSqlDto);
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public SqlConsoleVo lockTableByModelNo(String modelNo, String isLock) throws SQLException {

        TModelBaseInfoVo tModelBaseInfo = getModelBaseInfo(new BigDecimal(modelNo).longValue());
        TModelBase tModelBase = ModelConvertor.INSTANT.fromInfoVO(tModelBaseInfo);
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getMetadataSourceById(tModelBase.getSourceId());
        if (metadataSourceEntity == null) {
            throw new SQLException("数据源不存在");
        }
        String dbName = metadataSourceEntity.getDbSchema().getDbName();
        String sid = metadataSourceEntity.getDbSchema().getSid();
        SqlConsoleVo sqlConsoleVo = new SqlConsoleVo();
        sqlConsoleVo.setSuccess(true);
        sqlConsoleVo.setTime(new Date().getTime());
        if (isLock.equals("1")) {
            tModelBase.setIsLock(1);
            sqlConsoleVo.setSql("LOCK TABLES " + dbName + "." + sid + "." + tModelBase.getTName() + "READ");
        } else {
            tModelBase.setIsLock(0);
            //Todo  解锁
            sqlConsoleVo.setSql("UNLOCK TABLES " + dbName + "." + sid + "." + tModelBase.getTName());
        }
        updateById(tModelBase);
        return sqlConsoleVo;
    }

    @Override
    public List<ModelTableVo> modelAllTabByModelNo() {

        List<ModelTableVo> modelTableVos = new ArrayList<>();

        //查询模型管理模块中所有的表包含（数据源id,表id,字段列表）
        List<TModelBase> modelBases = list();

        //查询所有模型列表
        List<TModelColumn> tModelColumns = tModelColumnService.lambdaQuery().isNotNull(TModelColumn::getModelNo).list();

        if (modelBases != null && modelBases.size() > 0) {
            modelTableVos = modelBases.stream().map(modelBase -> {
                ModelTableVo modelTableVo = new ModelTableVo();
                modelTableVo.setModelId(modelBase.getId());
                modelTableVo.setTabName(modelBase.getTName());
                modelTableVo.setTableId(modelBase.getTableId());
                modelTableVo.setSourceId(modelBase.getSourceId());
                if (tModelColumns != null && tModelColumns.size() > 0){
                    modelTableVo.setModelColumns(tModelColumns.stream().filter(tModelColumn -> tModelColumn.getModelNo().equals(modelBase.getId())).map(TModelColumn::getTName).toList());
                }
                return modelTableVo;
            }).collect(Collectors.toList());
        }
        return modelTableVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TModelBaseInfoVo copy(ModelIdReqVo vo) {
        TModelBase tModelBase = getById(vo.getModelId());
        if (tModelBase == null) {
            throw new ModelException("模型不存在");
        }
        TModelBase copy = this.buildCopy(tModelBase);
        this.save(copy);
        //克隆字段表
        List<TModelColumn> modelColumns = tModelColumnService.lambdaQuery().eq(TModelColumn::getModelNo, copy.getId()).list();
        List<MetadataColumnMarketDto> metadataColumnEntities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(modelColumns)) {
            //保存模型字段
            for (TModelColumn tModelColumn : modelColumns) {
                tModelColumn.setModelNo(copy.getId());
                tModelColumn.setId(SnowflakeUtils.nextId());
                MetadataColumnMarketDto metadataColumnEntity = new MetadataColumnMarketDto();
                if (tModelColumn.getTitle().equals("id")) {
                    metadataColumnEntity.setColumnKey("1");
                }
                metadataColumnEntity.setSourceId(copy.getSourceId());
                metadataColumnEntity.setTableName(copy.getTName());
                metadataColumnEntity.setColumnName(tModelColumn.getTName());
                metadataColumnEntity.setColumnComment(tModelColumn.getRemark());
                metadataColumnEntity.setDataLength(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.getTLength(), 0)).toString());
                metadataColumnEntity.setDataPrecision(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.getDecimalDigits(), 0)).toString());
                metadataColumnEntity.setColumnNullable(tModelColumn.getIsNull() == 1 ? "Y" : "N");
                metadataColumnEntity.setActionType(ActionTypeEnum.ADD_COLUMN.getCode());
                metadataColumnEntities.add(metadataColumnEntity);
            }
            tModelColumnService.saveBatch(modelColumns);
        }
        //克隆主键
        TModelPkInfo primaryKey = tModelPrimaryKeyService.lambdaQuery().eq(TModelPkInfo::getModelNo, copy.getId()).last("limit 1").one();
         if (primaryKey != null) {
            primaryKey.setModelNo(copy.getId());
            primaryKey.setId(SnowflakeUtils.nextId());
            tModelPrimaryKeyService.save(primaryKey);
        }
        //克隆外键
        List<TModelFkInfo> foreignKeys = tModelForeignKeyService.lambdaQuery().eq(TModelFkInfo::getModelNo, copy.getId()).list();
         if (CollectionUtils.isNotEmpty(foreignKeys)) {
              for (TModelFkInfo foreignKey : foreignKeys) {
                 foreignKey.setModelNo(copy.getId());
                 foreignKey.setId(SnowflakeUtils.nextId());
             }
              tModelForeignKeyService.saveBatch(foreignKeys);
         }
        //克隆索引
        List<TModelIndex> metadataIndexVos = tModelIndexService.lambdaQuery().eq(TModelIndex::getModelNo, copy.getId()).list();
        if (CollectionUtils.isNotEmpty(metadataIndexVos)){
            for (TModelIndex index : metadataIndexVos) {
                index.setModelNo(copy.getId());
                index.setId(SnowflakeUtils.nextId());
            }
             tModelIndexService.saveBatch(metadataIndexVos);
        }
        // 运行sql
        if (!CreateTypeEnum.CREATE_TYPE_SQL.getCode().equals(copy.getCreateType())){
            String tableId = tableCreationUtils.createTableAndGetId(tModelBase, metadataColumnEntities);
            tModelBase.setTableId(tableId);
        }
        return ModelConvertor.INSTANT.toInfoVO(copy);
    }

    private TModelBase buildCopy(TModelBase tModelBase){
        TModelBase copy = new TModelBase();
        BeanUtils.copyProperties(tModelBase, copy);
        //克隆主表
        copy.setId(SnowflakeUtils.nextId());
        //根据克隆次数生成生成名字和代号
        Integer copyCount = Optional.ofNullable(tModelBase.getCopyCount()).orElse(0) + 1;
        //更新克隆次数
        tModelBase.setCopyCount(copyCount);
        this.updateById(tModelBase);
        //代号= copyCount 个 COPY_OF + 模型代号
        StringBuilder codePrefix = new StringBuilder();
        StringBuilder nameSuffix = new StringBuilder();
        for (int i = 0; i < copyCount; i++) {
            codePrefix.append(ModelConstants.CLONE_CODE_PREFIX);
            nameSuffix.append(ModelConstants.CLONE_NAME_SUFFIX);
        }
        copy.setCodeNo(codePrefix + tModelBase.getCodeNo());
        copy.setModelName(tModelBase.getModelName() + nameSuffix);
        copy.setTitle(tModelBase.getTitle() + nameSuffix);
        return copy;
    }


    @Override
    public TModelBaseInfoVo getModelBaseInfo(Long id) {
        TModelBase tModelBase = getById(id);
        return ModelConvertor.INSTANT.toInfoVO(tModelBase);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TModelBaseInfoVo update(TModelBaseInfoVo modelBase) {
        TModelBase tModelBase = ModelConvertor.INSTANT.fromInfoVO(modelBase);
        updateById(tModelBase);
        return ModelConvertor.INSTANT.toInfoVO(tModelBase);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(Long id) {
        //删除主键，外键，索引，字段表
        tModelPrimaryKeyService.remove(new LambdaQueryWrapper<TModelPkInfo>().eq(TModelPkInfo::getModelNo, id));
        tModelForeignKeyService.remove(new LambdaQueryWrapper<TModelFkInfo>().eq(TModelFkInfo::getModelNo, id));
        tModelIndexService.remove(new LambdaQueryWrapper<TModelIndex>().eq(TModelIndex::getModelNo, id));
        tModelColumnService.remove(new LambdaQueryWrapper<TModelColumn>().eq(TModelColumn::getModelNo, id));
        return removeById(id);
    }

    @Override
    public TModelQueryKeyVo queryModelKeyInfo(Long modelNo) {
        // 查询主键信息
        TModelPkInfo pkInfo = tModelPrimaryKeyService.lambdaQuery().eq(TModelPkInfo::getModelNo, modelNo).last("limit 1").one();

        // 查询外键信息 - 通过外键服务获取
        List<TModelFkInfo> fkInfoList = tModelForeignKeyService.getModelFkInfoByModelNo(modelNo);

        List<TModelFkInfoVo> fkInfoVoList = ModelFkInfoConvertor.INSTANT.toVO(fkInfoList);

        // 构建返回对象
        TModelQueryKeyVo queryKeyVo = new TModelQueryKeyVo();
        if (pkInfo != null) {
            queryKeyVo.setId(pkInfo.getId());
            queryKeyVo.setPColumn(pkInfo.getPColumn());
            queryKeyVo.setModelNo(pkInfo.getModelNo());
        }
        queryKeyVo.setFkList(fkInfoVoList);

        return queryKeyVo;
    }
}